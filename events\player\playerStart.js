const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue, track) => {
    try {
        if (!queue?.metadata?.send) return;

        // Safely extract track information with length limits
        const title = (track?.title || "Unknown Title").substring(0, 256);
        const url = track?.url || "N/A";
        const duration = track?.duration || "Unknown Duration";
        const author = (track?.author || "Unknown Artist").substring(0, 100);
        const requestedBy = track?.requestedBy ? `<@${track.requestedBy.id}>` : "Unknown User";
        const thumbnail = track?.thumbnail || "https://i.imgur.com/2n1cGDb.png";

        const embed = new EmbedBuilder()
            .setTitle("▶️ Now Playing")
            .setDescription(`**[${title}](${url})**`)
            .addFields(
                { name: "⏳ Duration", value: duration, inline: true },
                { name: "🎤 Artist", value: author, inline: true },
                { name: "🎧 Requested by", value: requestedBy, inline: true }
            )
            .setThumbnail(thumbnail)
            .setColor("#00FF00")
            .setTimestamp();

        // Send with timeout and cleanup
        const sendPromise = queue.metadata.send({ embeds: [embed] });

        // Add timeout to prevent hanging
        Promise.race([
            sendPromise,
            new Promise((_, reject) => setTimeout(() => reject(new Error('Send timeout')), 10000))
        ]).catch(error => {
            console.error("Error sending now playing embed:", error.message);
        });

    } catch (error) {
        console.error("Error in playerStart event:", error.message);
    }
};
