const handleRikaChat = require("../../plugin/javascript/rika");
const { suggestCommand } = require('../../utils/commandSuggest');
const {customCommandDB} = require('../../database/manager')

module.exports = async (client, message) => {
    if (message.author.bot) return;

    // Process AI chat in #rika-ai
    if (message.channel.id === process.env.channelId) {
        await handleRikaChat(client, message);
        return;
    }

    const mentionPrefixes = [`<@!${client.user.id}>`, `<@${client.user.id}>`];
    const isMentionPrefix = mentionPrefixes.some(p => message.content.startsWith(p));
    const prefix = isMentionPrefix ? mentionPrefixes.find(p => message.content.startsWith(p)) : '!';

    let args, commandName;

    // Handle bot mention without command
    if (isMentionPrefix && message.content.trim() === prefix) {
        return message.reply(
            `👋 Hai! Gunakan prefix \`!\` atau mention aku untuk menjalankan command.\n` +
            `📌 Cek daftar command lengkap di \`!help\`.`
        );
    }

    // Handle prefix-based commands
    if (message.content.startsWith('!') || isMentionPrefix) {
        args = message.content.slice(prefix.length).trim().split(/ +/);
        commandName = args.shift()?.toLowerCase();

        // Handle custom commands
        const customCommandData = await customCommandDB.get(`custom_${commandName}`);
        if (customCommandData && customCommandData.usePrefix) {
            const formattedResponse = customCommandData.response
                .replace(/{user}/g, `<@${message.author.id}>`)
                .replace(/{channel}/g, `<#${message.channel.id}>`)
                .replace(/{guild}/g, message.guild?.name || "this server");
            return message.reply(formattedResponse);
        }

        // Handle built-in commands
        if (
            !commandName || 
            (
                !client.textCommands.has(commandName) &&
                !client.textCommands.some(cmd => cmd.aliases?.includes(commandName))
            )
        ) {
            const filteredCommands = new Map([...client.textCommands].filter(([_, cmd]) => cmd.usePrefix !== false && !cmd.isEverywhere));
            const suggestion = suggestCommand(commandName || '', filteredCommands);
            return message.reply(
                suggestion 
                ? `❌ Command \`${prefix}${commandName || ''}\` does not exist! Did you mean \`${prefix}${suggestion}\`? Command list: \`!help\`.`
                : `❌ Command \`${prefix}${commandName || ''}\` does not exist! Command list: \`!help\`.`
            );
        }
    } else {
        // Memory-conscious text processing with length limits
        const content = message.content.length > 2000 ? message.content.substring(0, 2000) : message.content;
        const words = content.toLowerCase().split(/ +/).slice(0, 50); // Limit to 50 words max

        // Handle `isEverywhere` commands with optimized search
        for (const [name, command] of client.textCommands) {
            if (command.isEverywhere) {
                const hasCommand = words.includes(name);
                const hasAlias = command.aliases?.some(alias => words.includes(alias));

                if (hasCommand || hasAlias) {
                    commandName = name;
                    const commandIndex = hasCommand ? words.indexOf(name) : words.indexOf(command.aliases.find(alias => words.includes(alias)));
                    args = words.slice(commandIndex + 1);
                    break;
                }
            }
        }

        // Handle non-prefixed custom commands
        if (!commandName) {
            for (const word of words) {
                const customCommandData = await customCommandDB.get(`custom_${word}`);
                if (customCommandData && !customCommandData.usePrefix) {
                    const formattedResponse = customCommandData.response
                        .replace(/{user}/g, `<@${message.author.id}>`)
                        .replace(/{channel}/g, `<#${message.channel.id}>`)
                        .replace(/{guild}/g, message.guild?.name || "this server");
                    return message.reply(formattedResponse);
                }
            }
        }

        // Handle exact match `usePrefix: false` commands
        if (!commandName) {
            if (!client.textCommands.has(message.content.toLowerCase())) return; 
            commandName = message.content.toLowerCase();
            args = [];
        }
    }
    
    // Retrieve built-in command
    const command = client.textCommands.get(commandName) ||
                    client.textCommands.find(cmd => cmd.aliases?.includes(commandName));

    if (!command) return;

    // Ensure proper command execution
    if (command.isEverywhere || command.usePrefix === false || message.content.startsWith('!') || isMentionPrefix) {
        try {
            await command.execute(message, args);
        } catch (error) {
            console.error(error);
            message.reply('❌ Error executing command: ' + error.message);
        }
    }
};
