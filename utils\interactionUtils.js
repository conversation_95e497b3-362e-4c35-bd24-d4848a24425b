const { MessageFlags } = require('discord.js');

/**
 * Utility functions for handling Discord interactions safely
 */

/**
 * Check if an error is a recoverable Discord API error
 * @param {Error} error - The error to check
 * @returns {boolean} True if the error is recoverable
 */
function isRecoverableDiscordError(error) {
    const recoverableErrorCodes = [
        10062, // Unknown interaction
        40060, // Interaction has already been acknowledged
        10008, // Unknown message
        50001, // Missing access
        50013, // Missing permissions
        50035, // Invalid form body
    ];
    
    return error && recoverableErrorCodes.includes(error.code);
}

/**
 * Safely reply to an interaction with proper error handling
 * @param {Interaction} interaction - The Discord interaction
 * @param {object} options - Reply options
 * @returns {Promise<boolean>} True if reply was successful
 */
async function safeReply(interaction, options) {
    try {
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply(options);
            return true;
        } else if (interaction.deferred) {
            await interaction.editReply(options);
            return true;
        } else {
            // Interaction already replied, try to follow up
            await interaction.followUp(options);
            return true;
        }
    } catch (error) {
        if (isRecoverableDiscordError(error)) {
            console.warn(`⚠️ Could not reply to interaction (${error.code}): ${error.message}`);
        } else {
            console.error('❌ Unexpected error replying to interaction:', error);
        }
        return false;
    }
}

/**
 * Safely respond to an autocomplete interaction
 * @param {AutocompleteInteraction} interaction - The autocomplete interaction
 * @param {Array} choices - The autocomplete choices
 * @returns {Promise<boolean>} True if response was successful
 */
async function safeAutocompleteRespond(interaction, choices = []) {
    try {
        if (!interaction.responded) {
            await interaction.respond(choices);
            return true;
        }
    } catch (error) {
        if (isRecoverableDiscordError(error)) {
            console.warn(`⚠️ Could not respond to autocomplete (${error.code}): ${error.message}`);
        } else {
            console.error('❌ Unexpected error responding to autocomplete:', error);
        }
        return false;
    }
    return false;
}

/**
 * Handle interaction errors with appropriate logging and user feedback
 * @param {Error} error - The error that occurred
 * @param {Interaction} interaction - The Discord interaction
 * @param {string} context - Context of where the error occurred
 * @returns {Promise<void>}
 */
async function handleInteractionError(error, interaction, context = 'interaction') {
    // Check if it's a recoverable Discord API error
    if (isRecoverableDiscordError(error)) {
        console.warn(`⚠️ Recoverable ${context} error (${error.code}): ${error.message}`);
        return; // Don't try to reply to expired/invalid interactions
    }
    
    // Log the error
    console.error(`❌ ${context} error:`, error);
    
    // Try to inform the user
    const errorMessage = {
        content: `❌ An error occurred while processing your ${context}: ${error.message}`,
        flags: MessageFlags.Ephemeral
    };
    
    await safeReply(interaction, errorMessage);
}

/**
 * Wrap an interaction handler with error handling
 * @param {Function} handler - The interaction handler function
 * @param {string} context - Context for error reporting
 * @returns {Function} Wrapped handler function
 */
function wrapInteractionHandler(handler, context = 'interaction') {
    return async (interaction, ...args) => {
        try {
            await handler(interaction, ...args);
        } catch (error) {
            await handleInteractionError(error, interaction, context);
        }
    };
}

/**
 * Check if an interaction is still valid and can be responded to
 * @param {Interaction} interaction - The Discord interaction
 * @returns {boolean} True if the interaction is still valid
 */
function isInteractionValid(interaction) {
    // Check if interaction has expired (Discord interactions expire after 15 minutes)
    const now = Date.now();
    const interactionTime = interaction.createdTimestamp;
    const maxAge = 15 * 60 * 1000; // 15 minutes
    
    if (now - interactionTime > maxAge) {
        console.warn('⚠️ Interaction has expired');
        return false;
    }
    
    return true;
}

/**
 * Defer an interaction reply safely
 * @param {Interaction} interaction - The Discord interaction
 * @param {object} options - Defer options
 * @returns {Promise<boolean>} True if defer was successful
 */
async function safeDeferReply(interaction, options = {}) {
    try {
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply(options);
            return true;
        }
    } catch (error) {
        if (isRecoverableDiscordError(error)) {
            console.warn(`⚠️ Could not defer interaction (${error.code}): ${error.message}`);
        } else {
            console.error('❌ Unexpected error deferring interaction:', error);
        }
        return false;
    }
    return false;
}

/**
 * Create a timeout wrapper for interaction handlers
 * @param {Function} handler - The handler function
 * @param {number} timeoutMs - Timeout in milliseconds (default: 14 minutes)
 * @returns {Function} Wrapped handler with timeout
 */
function withTimeout(handler, timeoutMs = 14 * 60 * 1000) {
    return async (interaction, ...args) => {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error('Interaction handler timed out'));
            }, timeoutMs);
        });
        
        try {
            await Promise.race([
                handler(interaction, ...args),
                timeoutPromise
            ]);
        } catch (error) {
            if (error.message === 'Interaction handler timed out') {
                console.warn('⚠️ Interaction handler timed out');
                await safeReply(interaction, {
                    content: '❌ The operation took too long and was cancelled.',
                    flags: MessageFlags.Ephemeral
                });
            } else {
                throw error;
            }
        }
    };
}

module.exports = {
    isRecoverableDiscordError,
    safeReply,
    safeAutocompleteRespond,
    handleInteractionError,
    wrapInteractionHandler,
    isInteractionValid,
    safeDeferReply,
    withTimeout
};
