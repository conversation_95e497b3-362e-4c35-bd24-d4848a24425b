const { MessageFlags } = require("discord.js");
const {
    isRecoverableDiscordError,
    safeReply,
    safeAutocompleteRespond,
    handleInteractionError,
    isInteractionValid
} = require('../utils/interactionUtils');

module.exports = async (client, interaction) => {
    // Check if interaction is still valid before processing
    if (!isInteractionValid(interaction)) {
        return;
    }

    if (interaction.isChatInputCommand()) {
        const command = client.slashCommands.get(interaction.commandName);
        if (!command) return;

        const mainPlayer = require('discord-player').useMainPlayer();

        await mainPlayer.context.provide(
            { guild: interaction.guild },
            async () => {
                try {
                    await command.execute(interaction);
                } catch (error) {
                    await handleInteractionError(error, interaction, 'slash command');
                }
            }
        );
    } else if (interaction.isAutocomplete()) {
        const command = client.slashCommands.get(interaction.commandName);
        if (!command || !command.autocompleteRun) return;

        try {
            await command.autocompleteRun(interaction);
        } catch (error) {
            // Handle recoverable Discord API errors
            if (isRecoverableDiscordError(error)) {
                console.warn(`⚠️ Autocomplete interaction error (${error.code}) for command: ${interaction.commandName} - ${error.message}`);
                return; // Don't try to respond to expired/invalid interactions
            }

            console.error('❌ Autocomplete error:', error);

            // Try to respond with empty array as fallback
            await safeAutocompleteRespond(interaction, []);
        }
    }

    if (interaction.isButton()) {
        let customId = interaction.customId;
        let id = null;
    
        if (customId.includes('=')) {
            const parts = customId.split('=');
            customId = parts[0] + '='; 
            id = parts[1];
        }
    
        const button = client.buttons.get(customId);
        if (button) {
            try {
                await button.execute(client, interaction, id);
            } catch (error) {
                await handleInteractionError(error, interaction, 'button');
            }
        }
    }
    
    if (interaction.isStringSelectMenu()) {
        let customId = interaction.customId;
        let id = null;
    
        if (customId.includes('=')) {
            const parts = customId.split('=');
            customId = parts[0] + '='; 
            id = parts[1];
        }
    
        const selectMenu = client.selectMenus.get(customId);
        if (selectMenu) {
            try {
                await selectMenu.execute(client, interaction, id);
            } catch (error) {
                await handleInteractionError(error, interaction, 'select menu');
            }
        }
    }

    if (interaction.isContextMenuCommand()) {
        const contextCommand = client.contextMenus.get(interaction.commandName);
        if (!contextCommand) return;

        try {
            await contextCommand.execute(client, interaction);
        } catch (error) {
            await handleInteractionError(error, interaction, 'context menu');
        }
    }

    if (interaction.isModalSubmit()) {
        let customId = interaction.customId;
        let id = null;
    
        if (customId.includes('=')) {
            const parts = customId.split('=');
            customId = parts[0] + '='; 
            id = parts[1];
        }
    
        const modal = client.modals.get(customId);
        if (modal) {
            try {
                await modal.execute(client, interaction, id);
            } catch (error) {
                await handleInteractionError(error, interaction, 'modal');
            }
        }
    }
    
    
};
