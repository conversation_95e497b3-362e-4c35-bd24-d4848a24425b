const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue) => {
    try {
        if (!queue?.metadata?.send) return;

        const embed = new EmbedBuilder()
            .setTitle("🏁 Queue Finished")
            .setDescription("All songs have been played! Add more songs to keep the music going. 🎵")
            .setColor("#FF0000") // Red color
            .setTimestamp();

        // Send with timeout to prevent hanging
        Promise.race([
            queue.metadata.send({ embeds: [embed] }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Send timeout')), 5000))
        ]).catch(error => {
            console.error("Error sending queue finished embed:", error.message);
        });

    } catch (error) {
        console.error("Error in emptyQueue event:", error.message);
    }
};
