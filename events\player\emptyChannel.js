const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue) => {
    try {
        if (!queue?.metadata?.send) return;

        const embed = new EmbedBuilder()
            .setTitle("🚪 Empty Voice Channel")
            .setDescription("Everyone has left the voice channel, so the music has stopped. Join back and add songs to resume! 🎶")
            .setColor("#FFA500") // Orange color
            .setTimestamp();

        // Send with timeout to prevent hanging
        Promise.race([
            queue.metadata.send({ embeds: [embed] }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Send timeout')), 5000))
        ]).catch(error => {
            console.error("Error sending empty channel embed:", error.message);
        });

    } catch (error) {
        console.error("Error in emptyChannel event:", error.message);
    }
};
