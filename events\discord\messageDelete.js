const { snipeDB } = require("../../database/manager")
module.exports = async (client, message) => {
    try {
        if (!message?.guild || !message?.author || message.author.bot) return;

        // Limit content length to prevent memory issues
        const content = message.content ? message.content.substring(0, 2000) : null;

        const data = {
            content,
            authorTag: message.author.tag?.substring(0, 100) || 'Unknown',
            authorId: message.author.id,
            deletedAt: new Date().toISOString(),
            attachments: (message.attachments?.map(att => att.url) || []).slice(0, 5), // Limit to 5 attachments
            embeds: (message.embeds?.map(e => ({
                title: e.title?.substring(0, 256),
                description: e.description?.substring(0, 1000),
                url: e.url,
                image: e.image?.url,
                thumbnail: e.thumbnail?.url
            })) || []).slice(0, 3), // Limit to 3 embeds
        };

        const key = `snipe_${message.guild.id}_${message.author.id}`;
        let logs = await snipeDB.get(key) || [];

        logs.unshift(data);
        logs = logs.slice(0, 10); // Limit to 10

        await snipeDB.set(key, logs);
    } catch (error) {
        console.error("Error in messageDelete event:", error.message);
    }
};
