const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue, error) => {
    console.error(`❌ Player error: ${error?.stack || error?.message || error}`);

    try {
        if (!queue?.metadata?.send) return;

        // Extract only the first relevant part of the error message with better safety
        const rawMessage = error?.message || 'Unknown error';
        let errorMessage = rawMessage.split("\n")[0]; // Take only the first line

        // Limit length to avoid sending excessive error details
        if (errorMessage.length > 100) {
            errorMessage = errorMessage.substring(0, 97) + "..."; // Truncate long messages
        }

        const embed = new EmbedBuilder()
            .setTitle("❌ Error")
            .setDescription(`An error occurred while playing music:\n\`\`\`${errorMessage}\`\`\``)
            .setColor("#FF0000")
            .setTimestamp();

        // Send with timeout to prevent hanging
        Promise.race([
            queue.metadata.send({ embeds: [embed] }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Send timeout')), 5000))
        ]).catch(sendError => {
            console.error("Error sending player error embed:", sendError.message);
        });

    } catch (embedError) {
        console.error("Error in playerError event:", embedError.message);
    }
};
