const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue, error) => {
    console.error('Player error:', error?.message || error);

    try {
        if (!queue?.metadata?.send) return;

        // Sanitize error message to prevent excessive length
        const errorMessage = (error?.message || 'Unknown error').substring(0, 500);

        const errorEmbed = new EmbedBuilder()
            .setColor('#FF0000')
            .setTitle('❌ Playback Error')
            .setDescription(`An error occurred while playing music.\n\`\`\`${errorMessage}\`\`\``)
            .setTimestamp();

        // Send with timeout to prevent hanging
        Promise.race([
            queue.metadata.send({ embeds: [errorEmbed] }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Send timeout')), 5000))
        ]).catch(sendError => {
            console.error("Error sending player error embed:", sendError.message);
        });

    } catch (embedError) {
        console.error("Error in player error event:", embedError.message);
    }
};
