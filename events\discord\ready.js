const { ActivityType, EmbedBuilder } = require('discord.js');
const axios = require('axios');
const { checkYouTubeStatus } = require("../../plugin/javascript/hololive");
const fs = require('fs');
const path = require('path');


const wordlistPath = path.join(__dirname, '../../utils/indo-wordlist.txt');
const sentWordsPath = path.join(__dirname, '../../utils/sent-word.txt');
module.exports = (client) => {
    console.log(`✅ Bot is online as ${client.user.tag} (ID: ${client.user.id})`);

    // Function to update bot presence dynamically
    const statuses = [
        { name: 'Type /play', type: ActivityType.Listening },
        { name: `${client.guilds.cache.size} servers`, type: ActivityType.Watching },
        { name: 'with discord.js', type: ActivityType.Playing }
    ];

    let i = 0;
    const statusInterval = setInterval(() => {
        try {
            client.user.setPresence({
                activities: [statuses[i]],
                status: 'online'
            });
            i = (i + 1) % statuses.length;
        } catch (error) {
            console.error('❌ Error updating status:', error);
        }
    }, 15000); // Change every 15 seconds

    // Track interval for cleanup
    client.activeIntervals.add(statusInterval);
    console.log('✅ Status rotation initialized');

    // Function to send waifu image
    const channelId = process.env.botMainChannel;
    async function postWaifuImage() {
        const channel = client.channels.cache.get(channelId);
        if (!channel) return console.log('⚠️ Channel not found!');

        try {
            const response = await axios.get('https://api.waifu.pics/sfw/waifu');
            const imageUrl = response.data.url;

            const embed = new EmbedBuilder()
                .setTitle('Waifu every 30 minutes!')
                .setImage(imageUrl)
                .setColor('#FFC0CB')
                .setFooter({ text: 'Powered by waifu.pics' });

            await channel.send({ embeds: [embed] });
            console.log('✅ Waifu image posted');
        } catch (error) {
            console.error('❌ Error fetching waifu image:', error);
        }
    }

    // Post every 30 minutes (1800000 ms)
    const waifuInterval = setInterval(postWaifuImage, 1800000);

    // Track interval for cleanup
    client.activeIntervals.add(waifuInterval);
    console.log('✅ Waifu posting initialized');

    // Initialize YouTube status checker
    checkYouTubeStatus(client);
    const holoInterval = setInterval(() => {
        checkYouTubeStatus(client);
    }, 1000 * 60 * 2); // Check every 2 minutes

    // Track interval for cleanup
    client.activeIntervals.add(holoInterval);
    console.log('✅ Hololive status checker initialized');
};
