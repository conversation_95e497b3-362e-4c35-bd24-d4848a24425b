const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue) => {
    try {
        if (!queue?.metadata?.send) return;

        const embed = new EmbedBuilder()
            .setTitle("🚪 Disconnected")
            .setDescription("The bot has left the voice channel.")
            .setColor("#FF0000") // Red
            .setTimestamp();

        // Send with timeout to prevent hanging
        Promise.race([
            queue.metadata.send({ embeds: [embed] }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Send timeout')), 5000))
        ]).catch(error => {
            console.error("Error sending disconnect embed:", error.message);
        });

    } catch (error) {
        console.error("Error in disconnect event:", error.message);
    }
};
