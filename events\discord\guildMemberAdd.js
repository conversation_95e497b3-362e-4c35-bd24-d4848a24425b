const { <PERSON><PERSON>ow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Embed<PERSON><PERSON>er, AttachmentBuilder } = require("discord.js");
const Canvas = require("@napi-rs/canvas");
const path = require("path");

module.exports = async (client, member) => {
    console.log(`👤 ${member.user.tag} baru saja join!`);

    // Setup channel & role IDs
    const welcomeChannels = ["714239926635462737"]; // Bisa lebih dari satu channel
    const logChannelId = "714241289314631760";
    const autoRoleId = "714248484462657558";
    
    try {
        const guild = member.guild;
        const role = guild.roles.cache.get(autoRoleId);
        const logChannel = guild.channels.cache.get(logChannelId);

        // Pastikan channel dan role valid
        if (!role) console.log("⚠ Auto role tidak ditemukan!");
        if (!logChannel) console.log("⚠ Log channel tidak ditemukan!");

        // 🎭 Tambahkan role otomatis ke member baru
        if (role) await member.roles.add(role).catch(console.error);

        // 🎉 Ambil welcome channel secara random jika lebih dari satu
        const welcomeChannelId = welcomeChannels[Math.floor(Math.random() * welcomeChannels.length)];
        const channel = guild.channels.cache.get(welcomeChannelId);
        if (!channel) return console.log("⚠ Welcome channel tidak ditemukan!");

        // 📜 Random Fun Facts
        const funFacts = [

            "Gunakan `/help` untuk lihat semua fitur keren bot ini!"
        ];
        const randomFact = funFacts[Math.floor(Math.random() * funFacts.length)];

        // 🖼 Buat gambar welcome dengan memory optimization
        let canvas, ctx, background, avatar, attachment;

        try {
            canvas = Canvas.createCanvas(700, 250);
            ctx = canvas.getContext("2d");

            // Load images with error handling
            background = await Canvas.loadImage(path.join(__dirname, "welcome-bg.jpg")).catch(() => null);
            if (background) {
                ctx.drawImage(background, 0, 0, canvas.width, canvas.height);
            }

            // Teks sambutan
            ctx.font = "28px bold sans-serif";
            ctx.fillStyle = "#ffffff";
            ctx.shadowColor = "rgba(0, 0, 0, 0.8)";
            ctx.shadowBlur = 5;
            ctx.fillText(`Welcome, ${member.user.username}!`, canvas.width / 2.5, canvas.height / 1.8);

            // Avatar with error handling
            avatar = await Canvas.loadImage(member.user.displayAvatarURL({ format: "jpg", size: 128 })).catch(() => null);
            if (avatar) {
                ctx.beginPath();
                ctx.arc(125, 125, 50, 0, Math.PI * 2, true);
                ctx.closePath();
                ctx.clip();
                ctx.drawImage(avatar, 75, 75, 100, 100);
            }

            attachment = new AttachmentBuilder(await canvas.encode("png"), { name: "welcome.png" });
        } catch (canvasError) {
            console.error("❌ Canvas creation error:", canvasError);
            attachment = null; // Fallback to no image
        } finally {
            // Clean up canvas resources
            if (ctx) {
                ctx.clearRect(0, 0, canvas?.width || 0, canvas?.height || 0);
            }
            canvas = null;
            ctx = null;
            background = null;
            avatar = null;
        }

        // 📜 Embed Welcome
        const welcomeEmbed = new EmbedBuilder()
            .setColor("#ffcc00")
            .setTitle("🎉 Selamat Datang di Server!")
            .setDescription(`Hey <@${member.id}>, selamat datang di **${guild.name}**! 🎊\n\n📜 **Fakta Seru:** ${randomFact}\n\n📝 Jangan lupa baca peraturan di <#rules-channel-id> dan kenalan di <#general-channel-id>!`)
            .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
            .setImage("attachment://welcome.png")
            .setFooter({ text: `Total members: ${guild.memberCount} 👥` });

        // 🔘 Tombol Interaktif
        const buttons = new ActionRowBuilder().addComponents(
            new ButtonBuilder().setLabel("📜 Rules").setStyle(ButtonStyle.Link).setURL("https://discord.com/channels/YOUR_SERVER_ID/rules-channel-id"),
            new ButtonBuilder().setLabel("💬 Kenalan").setStyle(ButtonStyle.Link).setURL("https://discord.com/channels/YOUR_SERVER_ID/general-channel-id"),
            new ButtonBuilder().setLabel("❓ FAQ").setStyle(ButtonStyle.Link).setURL("https://discord.com/channels/YOUR_SERVER_ID/faq-channel-id")
        );

        // Send welcome message with optional attachment
        const messageOptions = { embeds: [welcomeEmbed], components: [buttons] };
        if (attachment) {
            messageOptions.files = [attachment];
        }
        await channel.send(messageOptions);

        // 📩 DM ke Member Baru
        await member.send(`👋 Halo **${member.user.username}**, selamat datang di **${guild.name}**!\n\n🎉 Kami punya banyak event menarik, jadi jangan lupa aktif di chat!\n💡 Kamu bisa mulai dengan baca peraturan di <#rules-channel-id> dan kenalan di <#general-channel-id>!\n\n🔥 Have fun! 🚀`).catch(() => console.log("Gagal mengirim DM"));

        // 📝 Logging ke Channel Admin
        if (logChannel) {
            const logEmbed = new EmbedBuilder()
                .setColor("#00ff00")
                .setTitle("✅ Member Baru Bergabung")
                .setDescription(`**${member.user.tag}** (${member.id}) telah bergabung ke server!`)
                .addFields(
                    { name: "📅 Bergabung Discord", value: `<t:${Math.floor(member.user.createdTimestamp / 1000)}:R>`, inline: true },
                    { name: "📜 Total Member", value: `${guild.memberCount}`, inline: true }
                )
                .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
                .setFooter({ text: `ID: ${member.id}` });

            await logChannel.send({ embeds: [logEmbed] });
        }

    } catch (err) {
        console.error("❌ Error handling member join:", err);
    }
};
